<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="# Laravel Authentication with Social Login

A complete Laravel authentication system with social login integration for Google, GitHub, and Facebook.

## Features

- **Traditional Authentication**: Email/password login and registration
- **Social Authentication**: Login with Google, GitHub, and Facebook
- **Modern UI**: Clean, responsive design with Tailwind CSS
- **Laravel Breeze**: Built on Laravel Breeze for authentication scaffolding
- **Laravel Socialite**: Integrated social authentication

## Screenshots

The application includes:
- Login page with social authentication buttons
- Registration page with social signup options
- Dashboard for authenticated users
- Profile management

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd <PERSON>vel
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database setup**
   ```bash
   php artisan migrate
   ```

5. **Build assets**
   ```bash
   npm run build
   ```

## Social Authentication Setup

### 1. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `http://localhost:8000/auth/google/callback`
6. Update `.env` file:
   ```env
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback
   ```

### 2. GitHub OAuth Setup

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Create a new OAuth App
3. Set Authorization callback URL: `http://localhost:8000/auth/github/callback`
4. Update `.env` file:
   ```env
   GITHUB_CLIENT_ID=your_github_client_id
   GITHUB_CLIENT_SECRET=your_github_client_secret
   GITHUB_REDIRECT_URI=http://localhost:8000/auth/github/callback
   ```

### 3. Facebook OAuth Setup

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Set Valid OAuth Redirect URIs: `http://localhost:8000/auth/facebook/callback`
5. Update `.env` file:
   ```env
   FACEBOOK_CLIENT_ID=your_facebook_client_id
   FACEBOOK_CLIENT_SECRET=your_facebook_client_secret
   FACEBOOK_REDIRECT_URI=http://localhost:8000/auth/facebook/callback
   ```

## Usage

1. **Start the development server**
   ```bash
   php artisan serve
   ```

2. **Access the application**
   - Visit `http://localhost:8000`
   - Click "Login" or "Register"
   - Choose traditional email/password or social authentication

## File Structure

### Key Files Added/Modified

- `app/Http/Controllers/Auth/SocialAuthController.php` - Handles social authentication
- `app/Models/User.php` - Updated to support social auth fields
- `database/migrations/0001_01_01_000000_create_users_table.php` - Added social auth columns
- `config/services.php` - Social provider configurations
- `routes/web.php` - Social authentication routes
- `resources/views/auth/login.blade.php` - Login page with social buttons
- `resources/views/auth/register.blade.php` - Registration page with social buttons

### Database Schema

The users table includes these additional fields for social authentication:
- `provider` - The social provider (google, github, facebook)
- `provider_id` - The user's ID from the social provider
- `avatar` - URL to the user's profile picture
- `password` - Made nullable for social-only users

## Routes

- `GET /auth/{provider}/redirect` - Redirect to social provider
- `GET /auth/{provider}/callback` - Handle social provider callback
- Standard Laravel Breeze routes for traditional authentication

## Security Features

- CSRF protection on all forms
- Password hashing for traditional users
- Email verification support
- Remember me functionality
- Secure social authentication flow

## Customization

### Adding More Social Providers

1. Install the provider package if needed
2. Add configuration to `config/services.php`
3. Update the `validateProvider` method in `SocialAuthController`
4. Add buttons to login/register views

### Styling

The application uses Tailwind CSS. Modify the views in `resources/views/auth/` to customize the appearance.

## Troubleshooting

### Common Issues

1. **Social login not working**
   - Check OAuth app settings in provider console
   - Verify redirect URIs match exactly
   - Ensure environment variables are set correctly

2. **Database errors**
   - Run `php artisan migrate:fresh` to reset database
   - Check database connection in `.env`

3. **Asset compilation issues**
   - Run `npm run build` to compile assets
   - Clear browser cache

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

This project is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT). Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## About Laravel

Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in many web projects, such as:

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache) storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com)**
- **[Tighten Co.](https://tighten.co)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Redberry](https://redberry.international/laravel-development)**
- **[Active Logic](https://activelogic.com)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
